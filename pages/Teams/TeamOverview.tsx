import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { type Team } from '@/types/teams';
import LogoImage from '@/components/k-components/LogoImage';
import { toast } from '@/toast/toast';
import ActionRequired from '@/components/k-components/ActionRequired';
import Jersey from '@/components/k-components/Jersey';

interface TeamOverviewProps {
  team: Team;
  tournamentId: string;
}

const TeamHeader = ({ team }: { team: Team }) => (
  <View className="flex flex-row items-center justify-between py-8 px-3 w-full">
    <View className="flex flex-row items-center gap-2 flex-1 min-w-0">
      <LogoImage
        logoUrl={team.logo_url}
        fallbackText={team.name}
        width={100}
        height={100}
        borderRadius={60}
        fallBacktextClassName="text-3xl font-urbanistBold"
      />
      <View className="flex flex-col justify-start flex-1 pr-2 min-w-0">
        <Text
          className="text-typography-700 font-urbanistBold text-3xl"
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {team?.name?.trim()}
        </Text>
        <Text className="text-lg font-urbanistSemiBold text-typography-600 tracking-widest">
          {team?.short_name?.trim()}
        </Text>
      </View>
    </View>
    {team?.jersey_color && (
      <Jersey
        color={team?.jersey_color}
        name={team.short_name}
        number="1"
        width={80}
        height={80}
      />
    )}
  </View>
);

const TeamOverview: React.FC<TeamOverviewProps> = ({ team, tournamentId }) => {
  return (
    <View className="flex-1 ">
      <TeamHeader team={team} />
      <View
        className="border border-outline-100 border-b-0 p-2 px-4 bg-background-0 h-full"
        style={{
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 6,
        }}
      >
        <ActionRequired
          actions={[
            {
              id: 'set-team-info',
              label: 'Set Team Info',
              description:
                'Add important details like team captain, coach name, and a contact number to finalize team setup.',
              condition: true,
              onPress: () => {
                toast.info('Team editing coming soon');
              },
            },
          ]}
        />
      </View>
    </View>
  );
};

export default TeamOverview;
